package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;

/**
 * Feature để zoom bằng phím tắt
 * <p>
 * LOGIC HOẠT ĐỘNG:
 * <p>
 * 1. HOLD-TO-ZOOM:
 *    - Giữ phím → Zoom (FOV = 30°)
 *    - Th<PERSON> phím → Trở về FOV gốc
 *    - Không toggle, chỉ hoạt động khi giữ phím
 * <p>
 * 2. KHI BẮT ĐẦU ZOOM:
 *    - Lưu FOV hiện tại làm originalFOV
 *    - Lưu mouse sensitivity hiện tại làm originalMouseSensitivity
 *    - Set FOV = zoom FOV (30° từ config)
 *    - Set mouse sensitivity = 20% của giá trị gốc
 *    - <PERSON><PERSON>h dấu isZoomed = true
 * <p>
 * 3. KHI KẾT THÚC ZOOM:
 *    - Khôi phục FOV về originalFOV
 *    - Khôi phục mouse sensitivity về originalMouseSensitivity
 *    - Đánh dấu isZoomed = false
 * <p>
 * 4. ĐẶC ĐIỂM:
 *    - Không hiển thị action messages (silent)
 *    - Không lưu trạng thái vào config
 *    - Reset zoom khi thoát game
 */
public class ZoomFeature {

    private final KeyBinding zoomKey;
    private boolean isZoomed = false;
    private int originalFOV = 70; // Giá trị fallback, sẽ được cập nhật từ game
    private double originalMouseSensitivity; // Sẽ được lấy từ cài đặt thực tế của người dùng

    // Hệ số giảm mouse sensitivity khi zoom (25% = 0.25)
    private static final double ZOOM_MOUSE_SENSITIVITY_MULTIPLIER = 0.25;

    public ZoomFeature() {
        // Đăng ký key binding cho hold-to-zoom
        this.zoomKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                TranslationKeys.TOGGLE_ZOOM,
                InputUtil.Type.KEYSYM,
                InputUtil.UNKNOWN_KEY.getCode(), // Không có phím mặc định
                TranslationKeys.QUICK_SETTING_CATEGORY
        ));
    }

    /**
     * Xử lý input từ bàn phím cho zoom feature
     */
    public void handleInput(MinecraftClient client) {
        if (client.player == null) return;

        // Kiểm tra trạng thái phím zoom (hold-to-zoom)
        boolean shouldZoom = zoomKey.isPressed();

        if (shouldZoom && !isZoomed) {
            // Bắt đầu zoom
            startZoom(client);
        } else if (!shouldZoom && isZoomed) {
            // Kết thúc zoom
            stopZoom(client);
        }
    }

    /**
     * Bắt đầu zoom (khi nhấn phím)
     */
    private void startZoom(MinecraftClient client) {
        // Lưu FOV hiện tại và set zoom FOV
        originalFOV = client.options.getFov().getValue();
        client.options.getFov().setValue(ModConfig.ZOOM_FOV);

        // Lưu mouse sensitivity thực tế từ cài đặt người dùng và giảm xuống 20%
        originalMouseSensitivity = client.options.getMouseSensitivity().getValue();
        double zoomMouseSensitivity = originalMouseSensitivity * ZOOM_MOUSE_SENSITIVITY_MULTIPLIER;
        client.options.getMouseSensitivity().setValue(zoomMouseSensitivity);

        isZoomed = true;

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Zoom started:");
            System.out.println("  - FOV set to: " + ModConfig.ZOOM_FOV);
            System.out.println("  - Mouse sensitivity: " + originalMouseSensitivity + " → " + zoomMouseSensitivity);
        }
    }

    /**
     * Kết thúc zoom (khi thả phím)
     */
    private void stopZoom(MinecraftClient client) {
        // Trở về FOV gốc
        client.options.getFov().setValue(originalFOV);

        // Khôi phục mouse sensitivity về giá trị gốc
        client.options.getMouseSensitivity().setValue(originalMouseSensitivity);

        isZoomed = false;

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Zoom stopped:");
            System.out.println("  - FOV restored to: " + originalFOV);
            System.out.println("  - Mouse sensitivity restored to: " + originalMouseSensitivity);
        }
    }

    /**
     * Reset zoom state (dùng khi cần cleanup)
     */
    public void resetZoom(MinecraftClient client) {
        if (isZoomed && client != null) {
            stopZoom(client);
        }
    }

    // Getters

    public String getFeatureName() {
        return TranslationKeys.ZOOM_FEATURE;
    }

    public KeyBinding getToggleZoomKey() {
        return zoomKey;
    }

    public boolean isEnabled() {
        return ModConfig.ENABLE_ZOOM;
    }

    /**
     * Kiểm tra xem có đang zoom không
     */
    public boolean isZoomed() {
        return isZoomed;
    }

    /**
     * Lấy mouse sensitivity hiện tại (để debug)
     */
    public double getCurrentMouseSensitivity(MinecraftClient client) {
        return client.options.getMouseSensitivity().getValue();
    }
}