package net.aethor.features;

import net.aethor.config.ModConfig;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.sound.SoundCategory;
import net.minecraft.text.Text;

/**
 * Abstract base class cho các sound features
 * <p>
 * LOGIC HOẠT ĐỘNG:
 * <p>
 * 1. KHỞI ĐỘNG GAME:
 *    - Tải cấu hình volume đã lưu từ config file
 *    - Kiểm tra setting hiện tại của người dùng trong game
 *    - Nếu volume game > 0 và khác với config → lưu lại volume mới
 * <p>
 * 2. KHI NHẤN PHÍM NÓNG:
 *    - Kiểm tra trạng thái thực tế: volume > 0 = đang bật, volume = 0 = đang tắt
 *    - Nếu đang BẬT → Lưu volume hiện tại (nế<PERSON> kh<PERSON> config) và tắt về 0
 *    - <PERSON>ế<PERSON> đang TẮT → Bật lên với volume đã lưu
 * <p>
 * 3. CONFIG CHỈ LỮU:
 *    - Volume người dùng cài đặt (để restore khi bật lại)
 *    - Không lưu trạng thái ON/OFF (luôn dựa vào game settings)
 * <p>
 * 4. KHÔNG CẦN:
 *    - Logic reset khi thoát game (khởi động đã xử lý)
 *    - Theo dõi thay đổi settings (phím nóng đã xử lý)
 *    - Lưu trạng thái ON/OFF (luôn kiểm tra thực tế)
 */
public abstract class AbstractSoundFeature {

    protected final KeyBinding toggleKey;

    // Lưu âm lượng người dùng cài đặt (để restore khi bật lại)
    protected double savedVolume = 1.0;



    // Flag để đảm bảo initialize chỉ được gọi một lần
    protected boolean isInitialized = false;

    public AbstractSoundFeature() {
        // Đăng ký key binding cho toggle sound
        this.toggleKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                getToggleKeyTranslation(),
                InputUtil.Type.KEYSYM,
                InputUtil.UNKNOWN_KEY.getCode(), // Không có phím mặc định
                getKeyCategoryTranslation()
        ));
    }

    /**
     * Khởi tạo và lưu giá trị gốc ban đầu của Minecraft
     * Gọi method này sau khi client đã sẵn sàng (chỉ một lần)
     */
    public void initialize(MinecraftClient client) {
        if (!isInitialized && client != null && client.options != null) {
            // Lấy volume hiện tại từ Minecraft
            double currentGameVolume = client.options.getSoundVolumeOption(getSoundCategory()).getValue();

            // Load volume đã lưu từ config
            double configVolume = getSavedOriginalVolume();

            if (configVolume < 0) {
                // Lần đầu tiên: lưu volume hiện tại nếu > 0, ngược lại dùng default
                savedVolume = (currentGameVolume > 0) ? currentGameVolume : getDefaultRestoreVolume();
                setSavedOriginalVolume(savedVolume);
            } else {
                // Đã có config: sử dụng volume đã lưu
                savedVolume = configVolume;

                // Kiểm tra nếu người dùng đã thay đổi setting và khác với config
                if (currentGameVolume > 0 && currentGameVolume != savedVolume) {
                    savedVolume = currentGameVolume;
                    setSavedOriginalVolume(savedVolume);
                }
            }

            isInitialized = true;

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Initialized " + getFeatureName() + " - Saved volume: " + savedVolume);
            }
        }
    }

    /**
     * Xử lý input từ bàn phím cho sound feature
     */
    public void handleInput(MinecraftClient client) {
        if (client.player == null) return;

        // Xử lý phím toggle
        while (toggleKey.wasPressed()) {
            toggleSound(client);
        }
    }



    /**
     * Toggle âm thanh - Logic đơn giản
     */
    private void toggleSound(MinecraftClient client) {
        // Kiểm tra trạng thái thực tế của game
        double currentGameVolume = client.options.getSoundVolumeOption(getSoundCategory()).getValue();

        if (currentGameVolume > 0) {
            // Âm thanh đang bật → Tắt đi
            // Nếu volume khác với config thì lưu lại
            if (currentGameVolume != savedVolume) {
                savedVolume = currentGameVolume;
                setSavedOriginalVolume(savedVolume);
            }

            // Tắt âm thanh
            setVolume(client, 0.0);
            showStateMessage(client, false);

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] " + getFeatureName() + " turned OFF - Saved volume: " + savedVolume);
            }
        } else {
            // Âm thanh đang tắt → Bật lên với volume đã lưu
            setVolume(client, savedVolume);
            showStateMessage(client, true);

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] " + getFeatureName() + " turned ON - Restored volume: " + savedVolume);
            }
        }
    }

    /**
     * Đặt âm lượng cho sound category
     */
    private void setVolume(MinecraftClient client, double volume) {
        client.options.getSoundVolumeOption(getSoundCategory()).setValue(volume);
    }

    /**
     * Hiển thị thông báo trạng thái hiện tại
     */
    private void showStateMessage(MinecraftClient client, boolean isOn) {
        if (client.player != null) {
            Text featureName = Text.translatable(getFeatureNameTranslation());
            Text statusText = Text.translatable(isOn ? getOnMessageTranslation() : getOffMessageTranslation());
            String color = isOn ? "§a" : "§c"; // Green for ON, Red for OFF
            
            client.player.sendMessage(
                Text.literal(featureName.getString() + "§7: " + color + statusText.getString()),
                true
            );
        }
    }

    // Getters

    public KeyBinding getToggleKey() {
        return toggleKey;
    }

    // Abstract methods - phải được implement bởi subclass

    /**
     * Lấy SoundCategory mà feature này điều khiển
     */
    protected abstract SoundCategory getSoundCategory();

    /**
     * Lấy translation key cho toggle key binding
     */
    protected abstract String getToggleKeyTranslation();

    /**
     * Lấy translation key cho key category
     */
    protected abstract String getKeyCategoryTranslation();

    /**
     * Lấy translation key cho tên feature (hiển thị trong message)
     */
    protected abstract String getFeatureNameTranslation();

    /**
     * Lấy translation key cho message ON
     */
    protected abstract String getOnMessageTranslation();

    /**
     * Lấy translation key cho message OFF
     */
    protected abstract String getOffMessageTranslation();

    /**
     * Lấy tên feature (dùng cho logging/debug)
     */
    public abstract String getFeatureName();

    /**
     * Kiểm tra xem feature có được enable không
     */
    public abstract boolean isEnabled();

    /**
     * Lấy default restore volume khi âm lượng ban đầu = 0
     */
    protected abstract double getDefaultRestoreVolume();

    /**
     * Get saved original volume từ config
     */
    protected abstract double getSavedOriginalVolume();

    /**
     * Save original volume vào config
     */
    protected abstract void setSavedOriginalVolume(double volume);

}
