package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.minecraft.sound.SoundCategory;

/**
 * Feature để tắt bật âm thanh nhạc nền bằng phím tắt
 * <p>
 * ĐIỀU KHIỂN: SoundCategory.MUSIC (background music)
 * <p>
 * LOGIC: <PERSON><PERSON> thừa từ AbstractSoundFeature
 * - Nhấn phím → Kiểm tra volume game hiện tại → Toggle ON/OFF
 * - Lưu volume người dùng cài đặt vào config để restore
 */
public class MusicSoundFeature extends AbstractSoundFeature {

    public MusicSoundFeature() {
        super();
    }

    // Abstract method implementations

    @Override
    protected SoundCategory getSoundCategory() {
        return SoundCategory.MUSIC;
    }

    @Override
    protected String getToggleKeyTranslation() {
        return TranslationKeys.TOGGLE_MUSIC_SOUND;
    }

    @Override
    protected String getKeyCategoryTranslation() {
        return TranslationKeys.QUICK_SETTING_CATEGORY;
    }

    @Override
    protected String getFeatureNameTranslation() {
        return TranslationKeys.MUSIC_SOUND_NAME;
    }

    @Override
    protected String getOnMessageTranslation() {
        return TranslationKeys.MESSAGE_ON;
    }

    @Override
    protected String getOffMessageTranslation() {
        return TranslationKeys.MESSAGE_OFF;
    }

    @Override
    public String getFeatureName() {
        return TranslationKeys.MUSIC_SOUND_FEATURE;
    }

    @Override
    public boolean isEnabled() {
        return ModConfig.ENABLE_MUSIC_SOUND;
    }

    @Override
    protected double getDefaultRestoreVolume() {
        return ModConfig.MUSIC_VOLUME;
    }

    @Override
    protected double getSavedOriginalVolume() {
        return ModConfig.getMusicOriginalVolume();
    }

    @Override
    protected void setSavedOriginalVolume(double volume) {
        ModConfig.setMusicOriginalVolume(volume);
    }




}
