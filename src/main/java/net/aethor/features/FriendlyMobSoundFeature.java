package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.minecraft.sound.SoundCategory;

/**
 * Feature để tắt bật âm thanh friendly mobs bằng phím tắt
 * <p>
 * ĐIỀU KHIỂN: SoundCategory.NEUTRAL (cow, pig, villager, etc.)
 * <p>
 * LOGIC: <PERSON>ế thừa từ AbstractSoundFeature
 * - Nhấn phím → Kiểm tra volume game hiện tại → Toggle ON/OFF
 * - Lưu volume người dùng cài đặt vào config để restore
 */
public class FriendlyMobSoundFeature extends AbstractSoundFeature {

    public FriendlyMobSoundFeature() {
        super();
    }

    // Abstract method implementations

    @Override
    protected SoundCategory getSoundCategory() {
        return SoundCategory.NEUTRAL;
    }

    @Override
    protected String getToggleKeyTranslation() {
        return TranslationKeys.TOGGLE_FRIENDLY_MOB_SOUND;
    }

    @Override
    protected String getKeyCategoryTranslation() {
        return TranslationKeys.QUICK_SETTING_CATEGORY;
    }

    @Override
    protected String getFeatureNameTranslation() {
        return TranslationKeys.FRIENDLY_MOB_SOUND_NAME;
    }

    @Override
    protected String getOnMessageTranslation() {
        return TranslationKeys.MESSAGE_ON;
    }

    @Override
    protected String getOffMessageTranslation() {
        return TranslationKeys.MESSAGE_OFF;
    }

    @Override
    public String getFeatureName() {
        return TranslationKeys.FRIENDLY_MOB_SOUND_FEATURE;
    }

    @Override
    public boolean isEnabled() {
        return ModConfig.ENABLE_FRIENDLY_MOB_SOUND;
    }

    @Override
    protected double getDefaultRestoreVolume() {
        return ModConfig.FRIENDLY_MOB_VOLUME;
    }

    @Override
    protected double getSavedOriginalVolume() {
        return ModConfig.getFriendlyMobOriginalVolume();
    }

    @Override
    protected void setSavedOriginalVolume(double volume) {
        ModConfig.setFriendlyMobOriginalVolume(volume);
    }




}
