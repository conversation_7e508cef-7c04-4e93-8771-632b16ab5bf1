package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.minecraft.sound.SoundCategory;

/**
 * Feature để tắt bật âm thanh hostile mobs bằng phím tắt
 * <p>
 * ĐIỀU KHIỂN: SoundCategory.HOSTILE (zombie, skeleton, creeper, etc.)
 * <p>
 * LOGIC: <PERSON>ế thừa từ AbstractSoundFeature
 * - Nhấn phím → Kiểm tra volume game hiện tại → Toggle ON/OFF
 * - Lưu volume người dùng cài đặt vào config để restore
 */
public class HostileMobSoundFeature extends AbstractSoundFeature {

    public HostileMobSoundFeature() {
        super();
    }

    // Abstract method implementations

    @Override
    protected SoundCategory getSoundCategory() {
        return SoundCategory.HOSTILE;
    }

    @Override
    protected String getToggleKeyTranslation() {
        return TranslationKeys.TOGGLE_HOSTILE_MOB_SOUND;
    }

    @Override
    protected String getKeyCategoryTranslation() {
        return TranslationKeys.QUICK_SETTING_CATEGORY;
    }

    @Override
    protected String getFeatureNameTranslation() {
        return TranslationKeys.HOSTILE_MOB_SOUND_NAME;
    }

    @Override
    protected String getOnMessageTranslation() {
        return TranslationKeys.MESSAGE_ON;
    }

    @Override
    protected String getOffMessageTranslation() {
        return TranslationKeys.MESSAGE_OFF;
    }

    @Override
    public String getFeatureName() {
        return TranslationKeys.HOSTILE_MOB_SOUND_FEATURE;
    }

    @Override
    public boolean isEnabled() {
        return ModConfig.ENABLE_HOSTILE_MOB_SOUND;
    }

    @Override
    protected double getDefaultRestoreVolume() {
        return ModConfig.HOSTILE_MOB_VOLUME;
    }

    @Override
    protected double getSavedOriginalVolume() {
        return ModConfig.getHostileMobOriginalVolume();
    }

    @Override
    protected void setSavedOriginalVolume(double volume) {
        ModConfig.setHostileMobOriginalVolume(volume);
    }




}