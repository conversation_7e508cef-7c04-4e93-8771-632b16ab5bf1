package net.aethor.config.core;

import com.google.gson.JsonObject;
import net.aethor.config.ModConfig; // Import ModConfig để dùng hằng số

public class ConfigUtils {

    private ConfigUtils() {
        // Utility class
    }

    /**
     * Lấy giá trị chuỗi từ config với đường dẫn lồng nhau (e.g., "mod_info.mod_id")
     *
     * @param config JsonObject để tìm giá trị.
     * @param path Đường dẫn đến giá trị.
     * @param defaultValue Giá trị mặc định nếu không tìm thấy.
     * @return Giá trị chuỗi hoặc giá trị mặc định.
     */
    public static String getStringValue(JsonObject config, String path, String defaultValue) {
        try {
            String[] parts = path.split("\\.");
            JsonObject current = config;

            for (int i = 0; i < parts.length - 1; i++) {
                if (!current.has(parts[i])) return defaultValue;
                current = current.getAsJsonObject(parts[i]);
            }

            String lastKey = parts[parts.length - 1];
            return current.has(lastKey) ? current.get(lastKey).getAsString() : defaultValue;
        } catch (Exception e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error getting string value for path '" + path + "': " + e.getMessage());
            return defaultValue;
        }
    }

    /**
     * Lấy giá trị int từ config với đường dẫn lồng nhau
     *
     * @param config JsonObject để tìm giá trị.
     * @param path Đường dẫn đến giá trị.
     * @param defaultValue Giá trị mặc định nếu không tìm thấy.
     * @return Giá trị int hoặc giá trị mặc định.
     */
    public static int getIntValue(JsonObject config, String path, int defaultValue) {
        try {
            String[] parts = path.split("\\.");
            JsonObject current = config;

            for (int i = 0; i < parts.length - 1; i++) {
                if (!current.has(parts[i])) return defaultValue;
                current = current.getAsJsonObject(parts[i]);
            }

            String lastKey = parts[parts.length - 1];
            return current.has(lastKey) ? current.get(lastKey).getAsInt() : defaultValue;
        } catch (Exception e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error getting int value for path '" + path + "': " + e.getMessage());
            return defaultValue;
        }
    }

    /**
     * Lấy giá trị double từ config với đường dẫn lồng nhau
     *
     * @param config JsonObject để tìm giá trị.
     * @param path Đường dẫn đến giá trị.
     * @param defaultValue Giá trị mặc định nếu không tìm thấy.
     * @return Giá trị double hoặc giá trị mặc định.
     */
    public static double getDoubleValue(JsonObject config, String path, double defaultValue) {
        try {
            String[] parts = path.split("\\.");
            JsonObject current = config;

            for (int i = 0; i < parts.length - 1; i++) {
                if (!current.has(parts[i])) return defaultValue;
                current = current.getAsJsonObject(parts[i]);
            }

            String lastKey = parts[parts.length - 1];
            return current.has(lastKey) ? current.get(lastKey).getAsDouble() : defaultValue;
        } catch (Exception e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error getting double value for path '" + path + "': " + e.getMessage());
            return defaultValue;
        }
    }

    /**
     * Lấy giá trị boolean từ config với đường dẫn lồng nhau
     *
     * @param config JsonObject để tìm giá trị.
     * @param path Đường dẫn đến giá trị.
     * @param defaultValue Giá trị mặc định nếu không tìm thấy.
     * @return Giá trị boolean hoặc giá trị mặc định.
     */
    public static boolean getBooleanValue(JsonObject config, String path, boolean defaultValue) {
        try {
            String[] parts = path.split("\\.");
            JsonObject current = config;

            for (int i = 0; i < parts.length - 1; i++) {
                if (!current.has(parts[i])) return defaultValue;
                current = current.getAsJsonObject(parts[i]);
            }

            String lastKey = parts[parts.length - 1];
            return current.has(lastKey) ? current.get(lastKey).getAsBoolean() : defaultValue;
        } catch (Exception e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error getting boolean value for path '" + path + "': " + e.getMessage());
            return defaultValue;
        }
    }

    /**
     * Hàm kẹp giá trị (clamp) vào một khoảng giới hạn.
     *
     * @param value Giá trị cần kẹp.
     * @param min Giá trị tối thiểu.
     * @param max Giá trị tối đa.
     * @return Giá trị đã được kẹp.
     */
    public static int clamp(int value, int min, int max) {
        return Math.max(min, Math.min(max, value));
    }
}