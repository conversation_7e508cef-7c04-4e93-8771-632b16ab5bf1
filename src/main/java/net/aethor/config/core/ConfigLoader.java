package net.aethor.config.core;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import net.aethor.config.ModConfig; // Import ModConfig để dùng hằng số

import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.file.Files;

public class ConfigLoader {

    private ConfigLoader() {
        // Utility class
    }

    /**
     * Tải config từ file (tạo từ template nếu chưa có).
     * <PERSON><PERSON> gồm cả việc kiểm tra version, migrate và validate cấu trúc.
     *
     * @return JsonObject chứa dữ liệu config đã tải.
     */
    public static JsonObject loadConfig() {
        if (!ModConfig.CONFIG_FILE.exists()) {
            copyTemplateToConfigFolder();
        }

        try (FileReader reader = new FileReader(ModConfig.CONFIG_FILE)) {
            JsonObject loadedConfig = JsonParser.parseReader(reader).getAsJsonObject();

            ConfigMigration.checkAndMigrateConfigVersion(loadedConfig); // Kiểm tra và migrate
            ConfigValidator.validateAndUpgradeConfig(loadedConfig); // Xác thực và nâng cấp

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Config loaded and validated successfully");
            }
            return loadedConfig;
        } catch (Exception e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error loading config: " + e.getMessage());
            // Fallback: load từ template nếu có lỗi
            return loadConfigFromTemplate();
        }
    }

    /**
     * Tải config từ template trong resources.
     *
     * @return JsonObject chứa dữ liệu config từ template.
     */
    public static JsonObject loadConfigFromTemplate() {
        try {
            InputStream inputStream = ModConfig.class.getResourceAsStream("/quicksettingkey.json");
            if (inputStream == null) {
                System.err.println("[" + ModConfig.MOD_NAME + "] Template not found, using empty config");
                return new JsonObject();
            }

            InputStreamReader reader = new InputStreamReader(inputStream);
            JsonObject templateConfig = JsonParser.parseReader(reader).getAsJsonObject();
            reader.close();
            inputStream.close();

            System.out.println("[" + ModConfig.MOD_NAME + "] Loaded config from template");
            return templateConfig;
        } catch (Exception e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error loading template: " + e.getMessage());
            return new JsonObject(); // Trả về JsonObject rỗng nếu có lỗi
        }
    }

    /**
     * Sao chép template từ resources sang thư mục config.
     */
    public static void copyTemplateToConfigFolder() {
        try {
            if (!ModConfig.CONFIG_DIR.toFile().exists()) {
                boolean created = ModConfig.CONFIG_DIR.toFile().mkdirs();
                if (!created) {
                    System.err.println("[" + ModConfig.MOD_NAME + "] Failed to create config directory: " + ModConfig.CONFIG_DIR);
                    return;
                }
            }

            try (InputStream inputStream = ModConfig.class.getResourceAsStream("/quicksettingkey.json")) {
                if (inputStream != null) {
                    Files.copy(inputStream, ModConfig.CONFIG_FILE.toPath());
                    if (ModConfig.DEBUG_MODE) {
                        System.out.println("[" + ModConfig.MOD_NAME + "] Created config from template");
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error copying template: " + e.getMessage());
        }
    }

    /**
     * Lưu dữ liệu config vào file.
     *
     * @param configData JsonObject chứa dữ liệu config cần lưu.
     */
    public static void saveConfigToFile(JsonObject configData) {
        try {
            // Kiểm tra xem thư mục config đã tồn tại chưa
            if (!ModConfig.CONFIG_DIR.toFile().exists()) {
                // Cố gắng tạo các thư mục cần thiết
                boolean created = ModConfig.CONFIG_DIR.toFile().mkdirs();
                // Nếu không thể tạo thư mục, in ra lỗi và thoát
                if (!created) {
                    System.err.println("[" + ModConfig.MOD_NAME + "] Failed to create config directory: " + ModConfig.CONFIG_DIR);
                    return; // Rất quan trọng: Thoát khỏi phương thức nếu không tạo được thư mục
                }
            }

            // Nếu thư mục tồn tại hoặc đã được tạo thành công, tiến hành ghi file
            try (FileWriter writer = new FileWriter(ModConfig.CONFIG_FILE)) {
                ModConfig.GSON.toJson(configData, writer);
            }
        } catch (IOException e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error saving config file: " + e.getMessage());
        }
    }
}