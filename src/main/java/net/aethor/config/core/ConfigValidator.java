package net.aethor.config.core;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import net.aethor.config.ModConfig; // Import ModConfig để dùng hằng số

import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

public class ConfigValidator {

    private ConfigValidator() {
        // Utility class
    }

    /**
     * Xác thực và nâng cấp file config - thêm key thiếu, xóa key cũ.
     *
     * @param currentConfig JsonObject chứa dữ liệu config hiện tại.
     */
    public static void validateAndUpgradeConfig(JsonObject currentConfig) {
        try {
            JsonObject templateConfig = loadTemplateConfig();
            if (templateConfig == null) {
                System.err.println("[" + ModConfig.MOD_NAME + "] Cannot load template for validation");
                return;
            }

            boolean configChanged = false;

            // Hợp nhất các key còn thiếu từ template
            configChanged |= mergeConfigRecursively(currentConfig, templateConfig);

            // Xóa các key lỗi thời (key không có trong template)
            configChanged |= removeObsoleteKeys(currentConfig, templateConfig);

            // Lưu lại file nếu có thay đổi
            if (configChanged) {
                ConfigLoader.saveConfigToFile(currentConfig);
                System.out.println("[" + ModConfig.MOD_NAME + "] Config file updated with new structure");
            }

            if (ModConfig.DEBUG_MODE && configChanged) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Config validation completed with changes");
            }
        } catch (Exception e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error during config validation: " + e.getMessage());
        }
    }

    /**
     * Tải template config từ resources.
     *
     * @return JsonObject chứa dữ liệu config từ template.
     */
    private static JsonObject loadTemplateConfig() {
        try {
            InputStream inputStream = ModConfig.class.getResourceAsStream("/quicksettingkey.json");
            if (inputStream == null) return null;

            InputStreamReader reader = new InputStreamReader(inputStream);
            JsonObject template = JsonParser.parseReader(reader).getAsJsonObject();
            reader.close();
            inputStream.close();

            return template;
        } catch (Exception e) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Error loading template for validation: " + e.getMessage());
            return null;
        }
    }

    /**
     * Hợp nhất các key còn thiếu từ template vào config hiện tại.
     *
     * @param current JsonObject config hiện tại.
     * @param template JsonObject config template.
     * @return true nếu có thay đổi.
     */
    private static boolean mergeConfigRecursively(JsonObject current, JsonObject template) {
        boolean changed = false;

        for (String key : template.keySet()) {
            if (!current.has(key)) {
                // Key thiếu - thêm từ template
                current.add(key, template.get(key).deepCopy());
                changed = true;

                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] Added missing key: " + key);
                }
            } else {
                // Key tồn tại - kiểm tra các đối tượng lồng nhau
                if (template.get(key).isJsonObject() && current.get(key).isJsonObject()) {
                    boolean nestedChanged = mergeConfigRecursively(
                            current.getAsJsonObject(key),
                            template.getAsJsonObject(key)
                    );
                    changed |= nestedChanged;
                }
            }
        }
        return changed;
    }

    /**
     * Xóa các key không có trong template (obsolete keys).
     *
     * @param current JsonObject config hiện tại.
     * @param template JsonObject config template.
     * @return true nếu có thay đổi.
     */
    private static boolean removeObsoleteKeys(JsonObject current, JsonObject template) {
        boolean changed = false;

        List<String> keysToRemove = new ArrayList<>();

        for (String key : current.keySet()) {
            if (!template.has(key)) {
                keysToRemove.add(key);
            } else if (template.get(key).isJsonObject() && current.get(key).isJsonObject()) {
                // Kiểm tra đệ quy các đối tượng lồng nhau
                boolean nestedChanged = removeObsoleteKeys(
                        current.getAsJsonObject(key),
                        template.getAsJsonObject(key)
                );
                changed |= nestedChanged;
            }
        }

        // Xóa các key lỗi thời
        for (String key : keysToRemove) {
            current.remove(key);
            changed = true;

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Removed obsolete key: " + key);
            }
        }
        return changed;
    }
}