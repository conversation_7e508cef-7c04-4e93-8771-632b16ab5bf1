package net.aethor.config.core;

import com.google.gson.JsonObject;
import net.aethor.config.ModConfig; // Import ModConfig

public class RuntimeConfigManager {

    private final JsonObject configData; // <PERSON><PERSON> chiếu đến configData gốc từ ModConfig
    private final RuntimeData runtimeData = new RuntimeData();

    public RuntimeConfigManager(JsonObject initialConfigData) {
        this.configData = initialConfigData;
        loadRuntimeData(); // Tải dữ liệu runtime khi khởi tạo
    }

    /**
     * Tải dữ liệu runtime từ config.
     */
    private void loadRuntimeData() {
        if (configData.has("fov")) {
            JsonObject fov = configData.getAsJsonObject("fov");
            if (fov.has("value")) {
                runtimeData.currentFOV = fov.get("value").getAsInt();
            }
        }

        if (configData.has("render_distance")) {
            JsonObject renderDistance = configData.getAsJsonObject("render_distance");
            if (renderDistance.has("value")) {
                runtimeData.currentRenderDistance = renderDistance.get("value").getAsInt();
            }
        }

        if (configData.has("simulation_distance")) {
            JsonObject simulationDistance = configData.getAsJsonObject("simulation_distance");
            if (simulationDistance.has("value")) {
                runtimeData.currentSimulationDistance = simulationDistance.get("value").getAsInt();
            }
        }

        // Tải sound volumes
        if (configData.has("sound_settings")) {
            JsonObject soundSettings = configData.getAsJsonObject("sound_settings");
            if (soundSettings.has("hostile_mob_volume")) {
                runtimeData.hostileMobOriginalVolume = soundSettings.get("hostile_mob_volume").getAsDouble();
            }
            if (soundSettings.has("friendly_mob_volume")) {
                runtimeData.friendlyMobOriginalVolume = soundSettings.get("friendly_mob_volume").getAsDouble();
            }
            if (soundSettings.has("music_volume")) {
                runtimeData.musicOriginalVolume = soundSettings.get("music_volume").getAsDouble();
            }
            if (soundSettings.has("jukebox_volume")) {
                runtimeData.jukeboxOriginalVolume = soundSettings.get("jukebox_volume").getAsDouble();
            }
        }
    }

    /**
     * Lưu config với các giá trị hiện tại.
     * Phương thức này sẽ đọc lại file để đảm bảo cấu trúc đầy đủ trước khi cập nhật và lưu.
     */
    public void saveConfig() {
        // Cần đảm bảo rằng chúng ta cập nhật trên một bản sao của configData đã được tải lại
        // để tránh ghi đè lên các thay đổi bên ngoài hoặc các trường chưa được runtimeData quản lý.
        JsonObject configToSave = ConfigLoader.loadConfig(); // Tải lại config để đảm bảo cấu trúc mới nhất
        if (configToSave != null) {
            updateCurrentValues(configToSave);
            ConfigLoader.saveConfigToFile(configToSave);
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Config saved successfully");
            }
        }
    }

    /**
     * Cập nhật các giá trị runtime vào JsonObject.
     */
    private void updateCurrentValues(JsonObject config) {
        if (config.has("fov")) {
            config.getAsJsonObject("fov").addProperty("value", runtimeData.currentFOV);
        }
        if (config.has("render_distance")) {
            config.getAsJsonObject("render_distance").addProperty("value", runtimeData.currentRenderDistance);
        }
        if (config.has("simulation_distance")) {
            config.getAsJsonObject("simulation_distance").addProperty("value", runtimeData.currentSimulationDistance);
        }

        // Cập nhật sound volumes
        if (config.has("sound_settings")) {
            JsonObject soundSettings = config.getAsJsonObject("sound_settings");
            soundSettings.addProperty("hostile_mob_volume", runtimeData.hostileMobOriginalVolume);
            soundSettings.addProperty("friendly_mob_volume", runtimeData.friendlyMobOriginalVolume);
            soundSettings.addProperty("music_volume", runtimeData.musicOriginalVolume);
            soundSettings.addProperty("jukebox_volume", runtimeData.jukeboxOriginalVolume);
        }
    }

    // ==================== FOV MANAGEMENT ====================

    public int getCurrentFOV() {
        return runtimeData.currentFOV;
    }

    public void setCurrentFOV(int fov) {
        runtimeData.currentFOV = fov; // Giá trị đã được clamp ở ModConfig
        saveConfig();
    }

    // ==================== RENDER DISTANCE MANAGEMENT ====================

    public int getCurrentRenderDistance() {
        return runtimeData.currentRenderDistance;
    }

    public void setCurrentRenderDistance(int distance) {
        runtimeData.currentRenderDistance = distance; // Giá trị đã được clamp ở ModConfig
        saveConfig();
    }

    // ==================== SIMULATION DISTANCE MANAGEMENT ====================

    public int getCurrentSimulationDistance() {
        return runtimeData.currentSimulationDistance;
    }

    public void setCurrentSimulationDistance(int distance) {
        runtimeData.currentSimulationDistance = distance; // Giá trị đã được clamp ở ModConfig
        saveConfig();
    }

    // ==================== SOUND VOLUME MANAGEMENT ====================

    public double getHostileMobOriginalVolume() {
        return runtimeData.hostileMobOriginalVolume;
    }

    public void setHostileMobOriginalVolume(double volume) {
        runtimeData.hostileMobOriginalVolume = volume;
        saveConfig();
    }

    public double getFriendlyMobOriginalVolume() {
        return runtimeData.friendlyMobOriginalVolume;
    }

    public void setFriendlyMobOriginalVolume(double volume) {
        runtimeData.friendlyMobOriginalVolume = volume;
        saveConfig();
    }

    public double getMusicOriginalVolume() {
        return runtimeData.musicOriginalVolume;
    }

    public void setMusicOriginalVolume(double volume) {
        runtimeData.musicOriginalVolume = volume;
        saveConfig();
    }

    public double getJukeboxOriginalVolume() {
        return runtimeData.jukeboxOriginalVolume;
    }

    public void setJukeboxOriginalVolume(double volume) {
        runtimeData.jukeboxOriginalVolume = volume;
        saveConfig();
    }

    /**
     * Lưu trữ dữ liệu runtime
     */
    private static class RuntimeData {
        // Cài đặt đồ họa - sẽ được tải từ config
        int currentFOV = 70;
        int currentRenderDistance = 12;
        int currentSimulationDistance = 12;

        // Âm lượng - lưu trữ âm lượng gốc để khôi phục (-1 = chưa khởi tạo)
        double hostileMobOriginalVolume = -1.0;
        double friendlyMobOriginalVolume = -1.0;
        double musicOriginalVolume = -1.0;
        double jukeboxOriginalVolume = -1.0;
    }
}