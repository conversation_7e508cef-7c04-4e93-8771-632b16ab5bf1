package net.aethor.config.core;

import com.google.gson.JsonObject;
import net.aethor.config.ModConfig; // Import ModConfig để dùng hằng số và DEBUG_MODE

public class ConfigMigration {

    private ConfigMigration() {
        // Utility class
    }

    /**
     * Kiểm tra phiên bản config và di chuyển nếu cần.
     *
     * @param configData JsonObject chứa dữ liệu config hiện tại.
     */
    public static void checkAndMigrateConfigVersion(JsonObject configData) {
        String currentVersion = ConfigUtils.getStringValue(configData, "mod_info.config_version", "");

        if (!ModConfig.CONFIG_VERSION.equals(currentVersion)) {
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Config version mismatch. Current: " +
                        currentVersion + ", Expected: " + ModConfig.CONFIG_VERSION);
            }

            // Cập nhật version trong config
            if (configData.has("mod_info")) {
                configData.getAsJsonObject("mod_info")
                        .addProperty("config_version", ModConfig.CONFIG_VERSION);
            }

            migrateConfigIfNeeded(configData, currentVersion);
        }
    }

    /**
     * Logic di chuyển config từ version cũ (nếu cần).
     *
     * @param configData JsonObject chứa dữ liệu config.
     * @param fromVersion Phiên bản config hiện tại.
     */
    private static void migrateConfigIfNeeded(JsonObject configData, String fromVersion) {
        switch (fromVersion) {
            case "1.0.5":
                // Di chuyển từ 1.0.5 -> 1.0.6a
                migrateTo106a(configData);
                break;
            case "":
                // Không có version = config rất cũ
                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] Migrating from legacy config (no version specified).");
                }
                break;
            default:
                if (!fromVersion.equals(ModConfig.CONFIG_VERSION)) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] Config version " + fromVersion +
                            " -> " + ModConfig.CONFIG_VERSION);
                }
        }
    }

    /**
     * Logic di chuyển cụ thể cho phiên bản 1.0.6a.
     *
     * @param configData JsonObject chứa dữ liệu config.
     */
    private static void migrateTo106a(JsonObject configData) {
        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Migrating config to version 1.0.6a");
        }
        // Thêm logic di chuyển cụ thể nếu cần (ví dụ: đổi tên key, thêm key mới với giá trị mặc định)
        // Ví dụ: if (configData.has("old_key")) { configData.add("new_key", configData.get("old_key")); configData.remove("old_key"); }
    }
}