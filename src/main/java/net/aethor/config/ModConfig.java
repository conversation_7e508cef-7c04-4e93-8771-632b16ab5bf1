package net.aethor.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import net.fabricmc.loader.api.FabricLoader;

import java.io.File;
import java.nio.file.Path;

import net.aethor.config.core.ConfigLoader;
import net.aethor.config.core.RuntimeConfigManager;
import net.aethor.config.core.ConfigUtils; // Thêm import này

/**
 * <PERSON><PERSON><PERSON> hình chung cho QuickSettingKeyMod
 * Quản lý cả default config và runtime config trong file duy nhất
 * Hỗ trợ validation, migration và auto-update config structure
 */
public class ModConfig {

    // Config file management
    public static final String CONFIG_FILE_NAME = "quicksettingkey.json";
    public static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    public static final Path CONFIG_DIR = FabricLoader.getInstance().getConfigDir();
    public static final File CONFIG_FILE = CONFIG_DIR.resolve(CONFIG_FILE_NAME).toFile();

    // Mod Information
    public static final String MOD_ID = "quicksettingkeymod";
    public static final String MOD_NAME = "QuickSettingKeyMod";
    public static final String CONFIG_VERSION = "1.0.6a";

    // Feature Enable/Disable Flags (sẽ được tải từ configData)
    public static final boolean ENABLE_RENDER_DISTANCE;
    public static final boolean ENABLE_SIMULATION_DISTANCE;
    public static final boolean ENABLE_FOV;
    public static final boolean ENABLE_BRIGHTNESS;
    public static final boolean ENABLE_ZOOM;
    public static final boolean ENABLE_HOSTILE_MOB_SOUND;
    public static final boolean ENABLE_FRIENDLY_MOB_SOUND;
    public static final boolean ENABLE_MUSIC_SOUND;
    public static final boolean ENABLE_JUKEBOX_SOUND;

    // Render Distance Settings
    public static final int MIN_RENDER_DISTANCE;
    public static final int MAX_RENDER_DISTANCE;
    public static final int RENDER_STEP;

    // Simulation Distance Settings
    public static final int MIN_SIMULATION_DISTANCE;
    public static final int MAX_SIMULATION_DISTANCE;
    public static final int SIMULATION_STEP;

    // FOV Settings
    public static final int MIN_FOV;
    public static final int MAX_FOV;
    public static final int FOV_STEP;
    public static final int ZOOM_FOV; // FOV khi zoom

    // Debouncing Settings
    public static final int DEBOUNCE_DELAY_MS; // milliseconds - delay before applying changes

    // Sound Settings
    public static final double HOSTILE_MOB_VOLUME;
    public static final double FRIENDLY_MOB_VOLUME;
    public static final double MUSIC_VOLUME;
    public static final double JUKEBOX_VOLUME;

    // Debug Settings
    public static final boolean DEBUG_MODE;

    // Runtime Data Manager
    private static RuntimeConfigManager runtimeConfigManager;
    private static JsonObject configData; // Dữ liệu cấu hình tải từ JSON

    // Static initializer - load config từ JSON
    static {
        configData = ConfigLoader.loadConfig(); // Tải config ban đầu
        runtimeConfigManager = new RuntimeConfigManager(configData); // Khởi tạo runtime manager

        // Feature Enable/Disable Flags
        ENABLE_RENDER_DISTANCE = ConfigUtils.getBooleanValue(configData, "features.render_distance", true);
        ENABLE_SIMULATION_DISTANCE = ConfigUtils.getBooleanValue(configData, "features.simulation_distance", true);
        ENABLE_FOV = ConfigUtils.getBooleanValue(configData, "features.fov", true);
        ENABLE_BRIGHTNESS = ConfigUtils.getBooleanValue(configData, "features.brightness", true);
        ENABLE_ZOOM = ConfigUtils.getBooleanValue(configData, "features.zoom", true);
        ENABLE_HOSTILE_MOB_SOUND = ConfigUtils.getBooleanValue(configData, "features.hostile_mob_sound", true);
        ENABLE_FRIENDLY_MOB_SOUND = ConfigUtils.getBooleanValue(configData, "features.friendly_mob_sound", true);
        ENABLE_MUSIC_SOUND = ConfigUtils.getBooleanValue(configData, "features.music_sound", true);
        ENABLE_JUKEBOX_SOUND = ConfigUtils.getBooleanValue(configData, "features.jukebox_sound", true);

        // Render Distance Settings
        MIN_RENDER_DISTANCE = ConfigUtils.getIntValue(configData, "render_distance.min", 2);
        MAX_RENDER_DISTANCE = ConfigUtils.getIntValue(configData, "render_distance.max", 32);
        RENDER_STEP = ConfigUtils.getIntValue(configData, "render_distance.step", 1);

        // Simulation Distance Settings
        MIN_SIMULATION_DISTANCE = ConfigUtils.getIntValue(configData, "simulation_distance.min", 5);
        MAX_SIMULATION_DISTANCE = ConfigUtils.getIntValue(configData, "simulation_distance.max", 32);
        SIMULATION_STEP = ConfigUtils.getIntValue(configData, "simulation_distance.step", 1);

        // FOV Settings
        MIN_FOV = ConfigUtils.getIntValue(configData, "fov.min", 30);
        MAX_FOV = ConfigUtils.getIntValue(configData, "fov.max", 110);
        FOV_STEP = ConfigUtils.getIntValue(configData, "fov.step", 5);
        ZOOM_FOV = ConfigUtils.getIntValue(configData, "fov.zoom_fov", 30);

        // Debouncing Settings
        DEBOUNCE_DELAY_MS = ConfigUtils.getIntValue(configData, "debounce.delay_ms", 2000);

        // Sound Settings
        HOSTILE_MOB_VOLUME = ConfigUtils.getDoubleValue(configData, "sound_settings.hostile_mob_volume", 1.0);
        FRIENDLY_MOB_VOLUME = ConfigUtils.getDoubleValue(configData, "sound_settings.friendly_mob_volume", 1.0);
        MUSIC_VOLUME = ConfigUtils.getDoubleValue(configData, "sound_settings.music_volume", 1.0);
        JUKEBOX_VOLUME = ConfigUtils.getDoubleValue(configData, "sound_settings.jukebox_volume", 1.0);

        // Debug Settings
        DEBUG_MODE = ConfigUtils.getBooleanValue(configData, "debug.enabled", false);
    }

    private ModConfig() {
        // Utility class - không cho phép khởi tạo
    }

    // ==================== RUNTIME DATA MANAGEMENT ====================

    /**
     * Lưu config với các giá trị hiện tại
     */
    public static void saveConfig() {
        runtimeConfigManager.saveConfig();
    }

    // ==================== FOV MANAGEMENT ====================

    public static int getCurrentFOV() {
        return runtimeConfigManager.getCurrentFOV();
    }

    public static void setCurrentFOV(int fov) {
        runtimeConfigManager.setCurrentFOV(ConfigUtils.clamp(fov, MIN_FOV, MAX_FOV));
    }

    // ==================== RENDER DISTANCE MANAGEMENT ====================

    public static int getCurrentRenderDistance() {
        return runtimeConfigManager.getCurrentRenderDistance();
    }

    public static void setCurrentRenderDistance(int distance) {
        runtimeConfigManager.setCurrentRenderDistance(ConfigUtils.clamp(distance, MIN_RENDER_DISTANCE, MAX_RENDER_DISTANCE));
    }

    // ==================== SIMULATION DISTANCE MANAGEMENT ====================

    public static int getCurrentSimulationDistance() {
        return runtimeConfigManager.getCurrentSimulationDistance();
    }

    public static void setCurrentSimulationDistance(int distance) {
        runtimeConfigManager.setCurrentSimulationDistance(ConfigUtils.clamp(distance, MIN_SIMULATION_DISTANCE, MAX_SIMULATION_DISTANCE));
    }

    // ==================== SOUND VOLUME MANAGEMENT ====================

    public static double getHostileMobOriginalVolume() {
        return runtimeConfigManager.getHostileMobOriginalVolume();
    }

    public static void setHostileMobOriginalVolume(double volume) {
        runtimeConfigManager.setHostileMobOriginalVolume(volume);
    }

    public static double getFriendlyMobOriginalVolume() {
        return runtimeConfigManager.getFriendlyMobOriginalVolume();
    }

    public static void setFriendlyMobOriginalVolume(double volume) {
        runtimeConfigManager.setFriendlyMobOriginalVolume(volume);
    }

    public static double getMusicOriginalVolume() {
        return runtimeConfigManager.getMusicOriginalVolume();
    }

    public static void setMusicOriginalVolume(double volume) {
        runtimeConfigManager.setMusicOriginalVolume(volume);
    }

    public static double getJukeboxOriginalVolume() {
        return runtimeConfigManager.getJukeboxOriginalVolume();
    }

    public static void setJukeboxOriginalVolume(double volume) {
        runtimeConfigManager.setJukeboxOriginalVolume(volume);
    }
}