# Quick Setting Key Mod

A Minecraft Fabric mod that provides convenient hotkeys for quickly adjusting game settings without opening the settings menu. Perfect for streamers, content creators, and players who frequently need to adjust their game settings on the fly.

## ⚙️ Features:

- **🎵 Sound Controls**: Toggle hostile mobs, friendly mobs, music, and jukebox sounds with dedicated hotkeys
- **🌟 Brightness Control**: Adjust gamma/brightness from 0% to 1600% with fullbright support
- **🔍 Zoom Feature**: Hold-to-zoom functionality for better visibility
- **📏 Distance Settings**: Quick adjustment of render distance (2-32 chunks) and simulation distance (5-32 chunks)
- **👁️ FOV Control**: Field of view adjustment from 30° to 110° with 5° increments
- **💾 Smart Config**: Automatically saves user preferences and restores them on game restart
- **⏱️ Debouncing**: Prevents setting spam with 2-second delay for distance/FOV changes
- **🎯 Real-time Feedback**: Shows current values and countdown timers for pending changes

## 📦 Requirements:

- **Minecraft**: 1.21.6
- **Fabric Loader**: 0.16.9 or higher
- **Fabric API**: Compatible version for 1.21.6
- **Java**: 21 or higher

## 🌍 Available Languages:

- **English (en_us)**: Full translation support
- **Vietnamese (vi_vn)**: Complete localization
- **Extensible**: Easy to add more languages via resource packs

## ⌨️ Setting Up Keybindings:

**Important**: This mod does not set any default hotkeys. You need to configure them manually in Minecraft's settings.

1. Go to **Options** → **Controls** → **Key Binds**
2. Scroll down to find the **"Quick Setting Key Mod"** category
3. Assign your preferred keys to each feature:
   - Brightness Increase/Decrease
   - Render Distance Increase/Decrease
   - Simulation Distance Increase/Decrease
   - FOV Increase/Decrease
   - Toggle Hostile Mob Sound
   - Toggle Friendly Mob Sound
   - Toggle Music Sound
   - Toggle Jukebox Sound
   - Zoom (Hold to activate)

*All keybindings are fully customizable to your preference.*

## 📥 Installation:

1. Install [Fabric Loader](https://fabricmc.net/use/installer/)
2. Download [Fabric API](https://modrinth.com/mod/fabric-api)
3. Download the latest release of Quick Setting Key Mod
4. Place both `.jar` files in your `mods` folder
5. Launch Minecraft and enjoy!

## 🔧 Configuration:

The mod automatically creates a `config/quicksettingkey.json` file to store your preferences. All settings are saved automatically when changed.

## 🐛 Issues & Support:

If you encounter any bugs or have feature requests, please report them on the [Issues](../../issues) page.

## 📄 License:

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
